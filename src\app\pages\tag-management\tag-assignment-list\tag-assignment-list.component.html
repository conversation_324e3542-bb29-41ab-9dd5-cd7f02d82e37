<!-- Tag Assignment List Component -->
<div class="local-management-container">
  <!-- Header Section -->
  <div class="header-section">
    <div class="page-title">
      <h1 class="title">
        <mat-icon class="title-icon">assignment</mat-icon> Liste des Affectations
      </h1>
    </div>
  </div>

  <div class="loading-spinner" *ngIf="isLoading">Chargement...</div>

  <!-- Assignments Table -->
  <div class="table-section" *ngIf="!isLoading">
    <div class="table-header">
      <div class="table-stats">
        <span class="stats-badge">{{ totalCount }} affectation(s)</span>
      </div>
    </div>

    <div *ngIf="assignments.length > 0; else noAssignments">
      <div class="table-container">
        <table class="assignment-table">
          <thead>
            <tr>
              <th>Tag</th>
              <th>Type de Cible</th>
              <th>Cible</th>
              <th>Date de Création</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let assignment of transformedAssignments" class="table-row">
              <td>
                <div class="tag-cell">
                  <mat-icon class="tag-icon">local_offer</mat-icon>
                  {{ assignment.TagName }}
                </div>
              </td>
              <td>
                <span class="target-type-badge" [class]="'type-' + assignment.TargetTypeDisplay.toLowerCase()">
                  {{ assignment.TargetTypeDisplay }}
                </span>
              </td>
              <td>{{ assignment.TargetName }}</td>
              <td>{{ assignment.CreatedAtFormatted }}</td>
              <td>
                <div class="action-buttons">
                  <button
                    class="btn-action btn-delete"
                    (click)="deleteAssignment(assignment)"
                    title="Supprimer"
                    mat-icon-button>
                    <mat-icon>delete</mat-icon>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <mat-paginator
        [length]="totalCount"
        [pageSize]="pageSize"
        [pageIndex]="currentPage"
        [pageSizeOptions]="[5, 10, 25, 50]"
        (page)="onPageChange($event)"
        aria-label="Select page">
      </mat-paginator>
    </div>

    <ng-template #noAssignments>
      <div class="empty-state">
        <mat-icon class="empty-icon">assignment</mat-icon>
        <h3>Aucune affectation trouvée</h3>
        <p>Aucune affectation de tag n'a été créée pour le moment</p>
      </div>
    </ng-template>
  </div>

  <ng-toast [position]="TOAST_POSITIONS.TOP_RIGHT"></ng-toast>
</div>
