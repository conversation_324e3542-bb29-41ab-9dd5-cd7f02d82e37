.local-management-container {
  width: 95%;
  margin: 30px auto;
  padding: 25px;
  background: linear-gradient(145deg, #ffffff, #f9f9f9);
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e8f5e8;
}

.page-title {
  flex: 1;
}

.title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  color: #2E7D32;
  font-size: 1.75rem;
  font-weight: 600;
}

.title-icon {
  color: var(--primary);
  font-size: 1.75rem;
}

.actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.create-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 3px 10px rgba(76, 175, 80, 0.3);
}

.create-button:hover {
  background: linear-gradient(135deg, #45a049, #3d8b40);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

.create-button:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(76, 175, 80, 0.3);
}

.action-icon {
  font-size: 1.1rem;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 20px;
  margin-right: 10px;
}

.table-section {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.table-header h2 {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  color: #2E7D32;
  font-size: 1.25rem;
  font-weight: 600;
}

.table-header h2 mat-icon {
  color: var(--primary);
}

.table-stats {
  display: flex;
  gap: 12px;
}

.stats-badge {
  background: var(--primary);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Custom table styling */
.table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.assignment-table {
  width: 100%;
  border-collapse: collapse;
}

.assignment-table th {
  background: #f8fafc;
  padding: 16px;
  text-align: left;
  font-weight: 600;
  color: #374151;
  border-bottom: 1px solid #e5e7eb;
}

.assignment-table td {
  padding: 16px;
  border-bottom: 1px solid #f3f4f6;
  vertical-align: middle;
}

.assignment-table tr:hover {
  background: #f9fafb;
}

.tag-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tag-icon {
  color: var(--primary);
  font-size: 18px;
}

.target-type-badge {
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.target-type-badge.type-client {
  background: #dbeafe;
  color: #1e40af;
}

.target-type-badge.type-site {
  background: #dcfce7;
  color: #166534;
}

.target-type-badge.type-local {
  background: #fef3c7;
  color: #92400e;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.btn-action {
  padding: 8px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-delete {
  background: #fee2e2;
  color: #dc2626;
}

.btn-delete:hover {
  background: #fecaca;
  transform: translateY(-1px);
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 4rem;
  color: #d1d5db;
  margin-bottom: 16px;
}

.empty-state h3 {
  margin: 0 0 8px 0;
  color: #374151;
  font-size: 1.25rem;
  font-weight: 600;
}

.empty-state p {
  margin: 0;
  font-size: 1rem;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Dialog Styles */
::ng-deep .tag-assignment-dialog .mat-mdc-dialog-container {
  padding: 0 !important;
  border-radius: 12px !important;
  overflow: hidden !important;
}

::ng-deep .tag-assignment-dialog .mat-mdc-dialog-surface {
  border-radius: 12px !important;
}

