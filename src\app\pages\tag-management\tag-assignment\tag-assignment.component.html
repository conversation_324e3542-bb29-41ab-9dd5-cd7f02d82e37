<!-- Tag Assignment Component -->
<div class="tag-assignment-container">
  <!-- Header Section -->
  <div class="header-card-container">
    <div class="header-section">
      <div class="page-title">
        <h1 class="title">
          <mat-icon class="title-icon">assignment</mat-icon>
          Affectation des Tags
        </h1>
        <p class="subtitle">Affecter des tags aux clients, sites et locaux</p>
      </div>
    </div>
  </div>

  <!-- Main Content -->
  <div class="content-container">
    <!-- Create Assignment Form -->
    <div class="form-card">
      <div class="form-header">
        <h2>
          <mat-icon>assignment_add</mat-icon>
          Nouvelle Affectation
        </h2>
      </div>

      <!-- Validation Errors -->
      <div class="validation-errors" *ngIf="showValidationErrors && assignmentForm.invalid">
        <div class="validation-errors-title">
          <mat-icon>error_outline</mat-icon>
          Erreurs de validation
        </div>
        <ul class="validation-errors-list">
          <li *ngIf="assignmentForm.get('idTag')?.invalid">
            <mat-icon>error</mat-icon>
            Veuillez sélectionner un tag
          </li>
          <li *ngIf="assignmentForm.get('targetType')?.invalid">
            <mat-icon>error</mat-icon>
            Veuillez sélectionner un type de cible
          </li>
          <li *ngIf="assignmentForm.get('targetId')?.invalid">
            <mat-icon>error</mat-icon>
            Veuillez sélectionner une cible
          </li>
        </ul>
      </div>

      <form [formGroup]="assignmentForm" (ngSubmit)="onSubmitAssignment()" class="assignment-form">
        <div class="form-grid">
          <!-- Tag Selection -->
          <div class="form-group">
            <label for="tagSearch">Tag <span class="required">*</span></label>
            <mat-form-field class="full-width">
              <input
                matInput
                formControlName="tagSearch"
                placeholder="Rechercher un tag..."
                [matAutocomplete]="tagAuto">

              <mat-autocomplete #tagAuto="matAutocomplete" [displayWith]="displayTagFn">
                <mat-option
                  *ngFor="let tag of filteredTags | async"
                  [value]="tag"
                  (onSelectionChange)="onTagSelected(tag)">
                  <mat-icon class="option-icon">local_offer</mat-icon>
                  {{ tag.Nom }}
                </mat-option>
              </mat-autocomplete>
            </mat-form-field>
            <div class="error-message"
                 *ngIf="showValidationErrors && assignmentForm.get('idTag')?.invalid">
              Veuillez sélectionner un tag
            </div>
          </div>

          <!-- Target Type Selection -->
          <div class="form-group">
            <label for="targetType">Type de Cible <span class="required">*</span></label>
            <mat-form-field class="full-width">
              <mat-select formControlName="targetType" (selectionChange)="onTargetTypeChange()" placeholder="Sélectionnez un type">
                <mat-option *ngFor="let type of targetTypes" [value]="type.value">
                  <mat-icon class="option-icon">{{ type.icon }}</mat-icon>
                  {{ type.label }}
                </mat-option>
              </mat-select>
            </mat-form-field>
            <div class="error-message"
                 *ngIf="showValidationErrors && assignmentForm.get('targetType')?.invalid">
              Veuillez sélectionner un type de cible
            </div>
          </div>

          <!-- Target Selection -->
          <div class="form-group">
            <label for="targetSearch">Cible <span class="required">*</span></label>
            <mat-form-field class="full-width">
              <input
                matInput
                formControlName="targetSearch"
                placeholder="Rechercher une cible..."
                [matAutocomplete]="targetAuto"
                [disabled]="!filteredTargets.length">

              <mat-autocomplete #targetAuto="matAutocomplete" [displayWith]="displayTargetFn">
                <mat-option
                  *ngFor="let target of filteredTargetOptions | async"
                  [value]="target"
                  (onSelectionChange)="onTargetSelected(target)">
                  <mat-icon class="option-icon">{{ getTargetIcon() }}</mat-icon>
                  {{ target.Name }}
                </mat-option>
              </mat-autocomplete>
            </mat-form-field>
            <div class="error-message"
                 *ngIf="showValidationErrors && assignmentForm.get('targetId')?.invalid">
              Veuillez sélectionner une cible
            </div>
          </div>
        </div>

        <div class="form-actions">
          <button type="button" class="btn-cancel" (click)="resetAssignmentForm()">
            Annuler
          </button>
          <button type="submit" class="btn-submit" [disabled]="isLoading">
            <mat-icon>assignment_add</mat-icon>
            Affecter
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Loading Overlay -->
  <div class="loading-overlay" *ngIf="isLoading">
    <div class="loading-spinner">
      <mat-icon class="spinning">refresh</mat-icon>
      <p>Chargement...</p>
    </div>
  </div>

  <ng-toast [position]="TOAST_POSITIONS.TOP_RIGHT"></ng-toast>
</div>
