import { Component, OnInit, Optional, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Observable } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { MatIconModule } from '@angular/material/icon';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';

import { MatSelectModule } from '@angular/material/select';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatCardModule } from '@angular/material/card';
import {
  NgToastComponent,
  NgToastService,
  TOAST_POSITIONS,
} from 'ng-angular-popup';
import { MatAutocompleteModule } from '@angular/material/autocomplete';

import { TagApiService } from '@app/core/services/administrative/tag.service';
import { TagAssignmentApiService } from '@app/core/services/administrative/tag-assignment.service';
import { ClientApiService } from '@app/core/services/administrative/client.service';
import { SiteApiService } from '@app/core/services/administrative/site.service';
import { LocalApiService } from '@app/core/services/administrative/local.service';

import { Tag } from '@app/core/models/tag';
import { TargetType } from '@app/core/models/TagAssignment';
import { Client } from '@app/core/models/client';
import { Site } from '@app/core/models/site';
import { Local } from '@app/core/models/local';

@Component({
  selector: 'app-tag-assignment',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatIconModule,
    MatSelectModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatCardModule,
    MatAutocompleteModule,
    NgToastComponent
  ],
  templateUrl: './tag-assignment.component.html',
  styleUrls: ['./tag-assignment.component.css']
})
export class TagAssignmentComponent implements OnInit {
  TOAST_POSITIONS = TOAST_POSITIONS;

  // Forms
  assignmentForm: FormGroup;

  // Data
  tags: Tag[] = [];
  clients: Client[] = [];
  sites: Site[] = [];
  locals: Local[] = [];
  filteredTargets: any[] = [];

  // Filtered observables for autocomplete
  filteredTags!: Observable<Tag[]>;
  filteredTargetOptions!: Observable<any[]>;

  // UI State
  isLoading = false;
  showValidationErrors = false;

  // Target types
  targetTypes = [
    { value: TargetType.Client, label: 'Client', icon: 'business' },
    { value: TargetType.Site, label: 'Site', icon: 'location_city' },
    { value: TargetType.Local, label: 'Local', icon: 'room' }
  ];

  constructor(
    private fb: FormBuilder,
    private toast: NgToastService,
    private tagService: TagApiService,
    private tagAssignmentService: TagAssignmentApiService,
    private clientService: ClientApiService,
    private siteService: SiteApiService,
    private localService: LocalApiService,
    @Optional() private dialogRef?: MatDialogRef<TagAssignmentComponent>
  ) {
    this.assignmentForm = this.createAssignmentForm();
  }

  ngOnInit(): void {
    this.setupFilteredObservables();
    this.loadInitialData();
  }

  private createAssignmentForm(): FormGroup {
    return this.fb.group({
      idTag: ['', Validators.required],
      tagSearch: [''],
      targetType: ['', Validators.required],
      targetId: ['', Validators.required],
      targetSearch: ['']
    });
  }

  private loadInitialData(): void {
    this.isLoading = true;

    Promise.all([
      this.loadTags(),
      this.loadClients(),
      this.loadSites(),
      this.loadLocals()
    ]).finally(() => {
      this.isLoading = false;
    });
  }

  private setupFilteredObservables(): void {
    // Setup filtered tags
    this.filteredTags = this.assignmentForm.get('tagSearch')!.valueChanges.pipe(
      startWith(''),
      map(value => {
        if (typeof value === 'string') {
          return this.filterTags(value);
        } else if (value && typeof value === 'object') {
          // If it's a Tag object, return empty array to show all options
          return this.tags;
        }
        return this.filterTags('');
      })
    );

    // Setup filtered targets
    this.filteredTargetOptions = this.assignmentForm.get('targetSearch')!.valueChanges.pipe(
      startWith(''),
      map(value => {
        if (typeof value === 'string') {
          return this.filterTargets(value);
        } else if (value && typeof value === 'object') {
          // If it's a target object, return all filtered targets
          return this.filteredTargets;
        }
        return this.filterTargets('');
      })
    );
  }

  private filterTags(value: string): Tag[] {
    const filterValue = value.toLowerCase();
    return this.tags.filter(tag =>
      tag.Nom.toLowerCase().includes(filterValue)
    );
  }

  private filterTargets(value: string): any[] {
    const filterValue = value.toLowerCase();
    return this.filteredTargets.filter(target =>
      target.Name.toLowerCase().includes(filterValue)
    );
  }

  private async loadTags(): Promise<void> {
    try {
      this.tags = await this.tagService.getAll().toPromise() || [];
    } catch (error) {
      console.error('Error loading tags:', error);
      this.showError('Erreur lors du chargement des tags');
    }
  }



  private async loadClients(): Promise<void> {
    try {
      this.clients = await this.clientService.getAll().toPromise() || [];
    } catch (error) {
      console.error('Error loading clients:', error);
    }
  }

  private async loadSites(): Promise<void> {
    try {
      this.sites = await this.siteService.getAll().toPromise() || [];
    } catch (error) {
      console.error('Error loading sites:', error);
    }
  }

  private async loadLocals(): Promise<void> {
    try {
      this.locals = await this.localService.getAll().toPromise() || [];
    } catch (error) {
      console.error('Error loading locals:', error);
    }
  }

  onTargetTypeChange(): void {
    const targetType = this.assignmentForm.get('targetType')?.value;
    this.assignmentForm.get('targetId')?.setValue('');
    this.assignmentForm.get('targetSearch')?.setValue('');

    switch (targetType) {
      case TargetType.Client:
        this.filteredTargets = this.clients;
        break;
      case TargetType.Site:
        this.filteredTargets = this.sites;
        break;
      case TargetType.Local:
        this.filteredTargets = this.locals;
        break;
      default:
        this.filteredTargets = [];
    }

    // Re-setup filtered observables for targets
    this.setupFilteredObservables();
  }

  onTagSelected(tag: Tag): void {
  this.assignmentForm.get('idTag')?.setValue(tag.Id);
  this.assignmentForm.get('tagSearch')?.setValue(tag);
}

  onTargetSelected(target: any): void {
  this.assignmentForm.get('targetId')?.setValue(target.Id);
  this.assignmentForm.get('targetSearch')?.setValue(target);
}

 displayTagFn(tag: Tag | string): string {
  if (typeof tag === 'string') {
    return tag;
  }
  return tag?.Nom || '';
}

displayTargetFn(target: any | string): string {
  if (typeof target === 'string') {
    return target;
  }
  return target?.Name || '';
}

  getTargetIcon(): string {
    const targetType = this.assignmentForm.get('targetType')?.value;
    const typeConfig = this.targetTypes.find(t => t.value === targetType);
    return typeConfig ? typeConfig.icon : 'help_outline';
  }

  isInDialogMode(): boolean {
    return !!this.dialogRef;
  }

  async onSubmitAssignment(): Promise<void> {
    this.showValidationErrors = true;

    if (this.assignmentForm.invalid) {
      return;
    }

    this.isLoading = true;

    try {
      const formValue = this.assignmentForm.value;

      console.log('Form value before processing:', formValue);
      console.log('TargetType value:', formValue.targetType);
      console.log('TargetType type:', typeof formValue.targetType);

      // Ensure TargetType is sent as string value
      const targetTypeString = this.getTargetTypeString(formValue.targetType);

      const assignmentData = {
        IdTag: formValue.idTag,
        TargetType: targetTypeString,
        TargetId: formValue.targetId
      };

      console.log('Assignment data being sent to API:', assignmentData);

      await this.tagAssignmentService.createAssignment(assignmentData).toPromise();

      this.showSuccess('Affectation créée avec succès');

      // Close dialog if opened in dialog mode
      if (this.dialogRef) {
        this.dialogRef.close(true);
      } else {
        this.resetAssignmentForm();
      }
    } catch (error) {
      console.error('Error creating assignment:', error);
      this.showError('Erreur lors de la création de l\'affectation');
    } finally {
      this.isLoading = false;
    }
  }

  private getTargetTypeString(targetType: any): string {
    // Handle different possible formats of TargetType
    if (typeof targetType === 'string') {
      return targetType;
    }

    // If it's a number (enum index), convert to string
    if (typeof targetType === 'number') {
      switch (targetType) {
        case 0:
          return 'Client';
        case 1:
          return 'Site';
        case 2:
          return 'Local';
        default:
          return 'Client'; // Default fallback
      }
    }

    // If it's the enum value, convert to string
    return targetType.toString();
  }



  resetAssignmentForm(): void {
    // If in dialog mode, close the dialog
    if (this.dialogRef) {
      this.dialogRef.close(false);
      return;
    }

    // Reset form controls individually to ensure proper clearing
    this.assignmentForm.patchValue({
      idTag: '',
      tagSearch: '',
      targetType: '',
      targetId: '',
      targetSearch: ''
    });

    // Mark form as pristine and untouched
    this.assignmentForm.markAsPristine();
    this.assignmentForm.markAsUntouched();

    // Reset related data
    this.filteredTargets = [];
    this.showValidationErrors = false;
  }

  private showSuccess(message: string): void {
    this.toast.success(message, 'Succès', 3000, false);
  }

  private showError(message: string): void {
    this.toast.warning(message, 'Erreur', 5000, false);
  }
}
