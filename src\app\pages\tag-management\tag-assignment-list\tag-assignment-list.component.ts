import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDialog, MatDialogModule } from '@angular/material/dialog';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import {
  NgToastComponent,
  NgToastService,
  TOAST_POSITIONS,
} from 'ng-angular-popup';
import { TagAssignmentApiService } from '@app/core/services/administrative/tag-assignment.service';

import { ConfirmationDialogComponent } from '@app/components/confirmation-dialog/confirmation-dialog.component';

@Component({
  selector: 'app-tag-assignment-list',
  standalone: true,
  imports: [
    CommonModule,
    MatIconModule,
    MatPaginatorModule,
    NgToastComponent,
    MatDialogModule,
    MatButtonModule
  ],
  templateUrl: './tag-assignment-list.component.html',
  styleUrls: ['./tag-assignment-list.component.css']
})
export class TagAssignmentListComponent implements OnInit {
  TOAST_POSITIONS = TOAST_POSITIONS;

  // Data
  assignments: any[] = []; // Using any[] since the view endpoint returns different structure

  // Pagination
  currentPage = 0;
  pageSize = 10;
  totalCount = 0;
  paginatedAssignments: any[] = [];

  // UI State
  isLoading = false;

  constructor(
    readonly toast: NgToastService,
    readonly dialog: MatDialog,
    readonly tagAssignmentService: TagAssignmentApiService
  ) {}

  ngOnInit(): void {
    this.loadAssignments();
  }

  private async loadAssignments(): Promise<void> {
    this.isLoading = true;
    try {
      this.assignments = await this.tagAssignmentService.getAssignmentView().toPromise() || [];
      this.totalCount = this.assignments.length;
      this.updatePaginatedData();
    } catch (error) {
      console.error('Error loading assignments:', error);
      this.showError('Erreur lors du chargement des affectations');
    } finally {
      this.isLoading = false;
    }
  }

  private updatePaginatedData(): void {
    const startIndex = this.currentPage * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.paginatedAssignments = this.assignments.slice(startIndex, endIndex);
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.updatePaginatedData();
  }



  // Transform assignments for table display
  get transformedAssignments(): any[] {
    return this.paginatedAssignments.map((assignment: any) => ({
      ...assignment,
      TagName: assignment.Nom, // Tag name from the view
      TargetName: assignment.TargetName, // Target name from the view
      TargetTypeDisplay: assignment.TargetType, // Target type from the view (already as string)
      CreatedAtFormatted: assignment.CreatedAt ?
        new Date(assignment.CreatedAt).toLocaleDateString('fr-FR', {
          year: 'numeric',
          month: 'short',
          day: 'numeric'
        }) : '-'
    }));
  }



  deleteAssignment(assignment: any): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      width: '400px',
      data: {
        title: 'Confirmer la suppression',
        message: 'Êtes-vous sûr de vouloir supprimer cette affectation ?',
        icon: 'delete',
      },
    });

    dialogRef.afterClosed().subscribe(async (result) => {
      if (result && assignment.Id) {
        this.isLoading = true;
        
        try {
          await this.tagAssignmentService.delete(assignment.Id).toPromise();
          this.showSuccess('Affectation supprimée avec succès');
          await this.loadAssignments();
        } catch (error) {
          console.error('Error deleting assignment:', error);
          this.showError('Erreur lors de la suppression de l\'affectation');
        } finally {
          this.isLoading = false;
        }
      }
    });
  }





  private showSuccess(message: string): void {
    this.toast.success(message, 'Succès', 3000, false);
  }

  private showError(message: string): void {
    this.toast.warning(message, 'Erreur', 5000, false);
  }
}
